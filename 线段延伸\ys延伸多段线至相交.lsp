; 命令ys：选中两条多段线，找到最近的起始点并延伸
(defun c:ys ()
  (prompt "\n请选择两条多段线...")
  (setq ss (ssget '((0 . "LWPOLYLINE"))))
  (if (and ss (= (sslength ss) 2))
    (progn
      (setq ent1 (ssname ss 0))
      (setq ent2 (ssname ss 1))
      (setq pl1 (entget ent1))
      (setq pl2 (entget ent2))
      (setq pt1 (cdr (assoc 10 pl1))) ; 第一条多段线起点
      (setq pt2 (cdr (assoc 10 pl2))) ; 第二条多段线起点
      ; 计算距离，确定最近的两个点
      (if (< (distance pt1 pt2) (distance pt2 pt1))
        (progn (setq p1 pt1 p2 pt2))
        (progn (setq p1 pt2 p2 pt1))
      )
      ; 用extend命令延伸p1和p2
      (command "_.extend" ent1 "b" ent2 "b" p1 "" p2 "")
    )
    (prompt "\n请选择两条多段线！")
  )
  (princ)
)
