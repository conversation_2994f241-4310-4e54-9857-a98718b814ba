{"version": 3, "targets": {"net8.0": {"Microsoft.VisualStudio.Azure.Containers.Tools.Targets/1.22.1": {"type": "package", "build": {"build/Microsoft.VisualStudio.Azure.Containers.Tools.Targets.props": {}, "build/Microsoft.VisualStudio.Azure.Containers.Tools.Targets.targets": {}}}}}, "libraries": {"Microsoft.VisualStudio.Azure.Containers.Tools.Targets/1.22.1": {"sha512": "EfYANhAWqmWKoLwN6bxoiPZSOfJSO9lzX+UrU6GVhLhPub1Hd+5f0zL0/tggIA6mRz6Ebw2xCNcIsM4k+7NPng==", "type": "package", "path": "microsoft.visualstudio.azure.containers.tools.targets/1.22.1", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "EULA.md", "ThirdPartyNotices.txt", "build/Container.props", "build/Container.targets", "build/Microsoft.VisualStudio.Azure.Containers.Tools.Targets.props", "build/Microsoft.VisualStudio.Azure.Containers.Tools.Targets.targets", "build/Rules/GeneralBrowseObject.xaml", "build/Rules/cs-CZ/GeneralBrowseObject.xaml", "build/Rules/de-DE/GeneralBrowseObject.xaml", "build/Rules/es-ES/GeneralBrowseObject.xaml", "build/Rules/fr-FR/GeneralBrowseObject.xaml", "build/Rules/it-IT/GeneralBrowseObject.xaml", "build/Rules/ja-JP/GeneralBrowseObject.xaml", "build/Rules/ko-KR/GeneralBrowseObject.xaml", "build/Rules/pl-PL/GeneralBrowseObject.xaml", "build/Rules/pt-BR/GeneralBrowseObject.xaml", "build/Rules/ru-RU/GeneralBrowseObject.xaml", "build/Rules/tr-TR/GeneralBrowseObject.xaml", "build/Rules/zh-CN/GeneralBrowseObject.xaml", "build/Rules/zh-TW/GeneralBrowseObject.xaml", "build/ToolsTarget.props", "build/ToolsTarget.targets", "icon.png", "microsoft.visualstudio.azure.containers.tools.targets.1.22.1.nupkg.sha512", "microsoft.visualstudio.azure.containers.tools.targets.nuspec", "tools/Microsoft.VisualStudio.Containers.Tools.Common.dll", "tools/Microsoft.VisualStudio.Containers.Tools.Shared.dll", "tools/Microsoft.VisualStudio.Containers.Tools.Tasks.dll", "tools/Newtonsoft.Json.dll", "tools/System.Security.Principal.Windows.dll", "tools/cs/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/cs/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/cs/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/de/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/de/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/de/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/es/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/es/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/es/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/fr/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/fr/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/fr/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/it/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/it/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/it/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/ja/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/ja/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/ja/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/ko/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/ko/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/ko/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/pl/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/pl/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/pl/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/pt-BR/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/pt-BR/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/pt-BR/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/ru/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/ru/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/ru/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/tr/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/tr/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/tr/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/zh-Hans/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/zh-Hans/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/zh-Hans/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/zh-Hant/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/zh-Hant/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/zh-Hant/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll"]}}, "projectFileDependencyGroups": {"net8.0": ["Microsoft.VisualStudio.Azure.Containers.Tools.Targets >= 1.22.1"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "E:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\vscode\\w样式\\w样式.csproj", "projectName": "w样式", "projectPath": "D:\\vscode\\w样式\\w样式.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\vscode\\w样式\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.VisualStudio.Azure.Containers.Tools.Targets": {"target": "Package", "version": "[1.22.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}