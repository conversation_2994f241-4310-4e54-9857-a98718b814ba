'标题：章一章、第一节、第1条、、（1）、a.编号
Sub ChangeParagraphStyle() ' 更改段落样式的主过程
    ' 重置大纲编号库中的第5个编号模板
    ListGalleries(wdOutlineNumberGallery).reset(5) ' 重置第5个大纲编号模板，避免旧设置影响
    ' ====================标题1：编号+样式设置====================
    With ListGalleries(wdOutlineNumberGallery).ListTemplates(5).ListLevels(1) ' 设置第1级标题编号样式
        .NumberFormat = "第%1章" ' 编号格式为“第X章”
        .NumberStyle = 37 ' 中文大写数字编号样式
        .NumberPosition = CentimetersToPoints(0) ' 编号左缩进0厘米
        .Alignment = wdListLevelAlignCenter ' 编号居中对齐
        .TextPosition = CentimetersToPoints(0) ' 标题文本缩进0厘米
        .TabPosition = wdUndefined ' 制表位未定义
        .ResetOnHigher = 0 ' 不受更高级别影响
        .StartAt = 1 ' 从1开始编号
        .LinkedStyle = "标题 1" ' 关联到“标题 1”样式
    End With
    With ActiveDocument.Styles("标题 1").Font ' 设置标题1字体
        .NameFarEast = "黑体" ' 中文字体为黑体
        .NameAscii = "黑体" ' 英文及数字字体为黑体
        .NameOther = "黑体" ' 其他语言字体为黑体
        .Name = "黑体" ' 字体名称
        .Size = 15 ' 字号15磅
        .Bold = True ' 加粗
        .Color = wdColorBlack ' 字体颜色黑色
    End With
    With ActiveDocument.Styles("标题 1").ParagraphFormat ' 设置标题1段落格式
        .Alignment = wdAlignParagraphCenter ' 段落居中对齐
        .SpaceBefore = 24 ' 段前24磅
        .SpaceAfter = 18 ' 段后18磅
        .LeftIndent = 0 ' 左缩进0
    End With
    ' ====================标题2：编号+样式设置====================
    With ListGalleries(wdOutlineNumberGallery).ListTemplates(5).ListLevels(2) ' 设置第2级标题编号样式
        .NumberFormat = "第%2节、" ' 编号格式为“第X节、”
        .NumberStyle = 37 ' 中文大写数字编号样式
        .NumberPosition = CentimetersToPoints(0) ' 编号左缩进0厘米
        .Alignment = wdListLevelAlignLeft ' 编号左对齐
        .TextPosition = CentimetersToPoints(0) ' 标题文本缩进0厘米
        .TabPosition = wdUndefined ' 制表位未定义
        .ResetOnHigher = 0 ' 不受更高级别影响
        .StartAt = 1 ' 从1开始编号
        .LinkedStyle = "标题 2" ' 关联到“标题 2”样式
    End With
    With ActiveDocument.Styles("标题 2").Font ' 设置标题2字体
        .NameFarEast = "宋体" ' 中文字体为宋体
        .NameAscii = "宋体" ' 英文及数字字体为宋体
        .NameOther = "宋体" ' 其他语言字体为宋体
        .Name = "宋体" ' 字体名称
        .Size = 14 ' 字号14磅
        .Bold = True ' 加粗
        .Color = wdColorBlack ' 字体颜色黑色
    End With
    With ActiveDocument.Styles("标题 2").ParagraphFormat ' 设置标题2段落格式
        .Alignment = wdAlignParagraphCenter ' 段落居中对齐
        .SpaceBefore = 12 ' 段前12磅
        .SpaceAfter = 6 ' 段后6磅
        .LeftIndent = 0 ' 左缩进0
    End With
    ' ====================标题3：编号+样式设置====================
    With ListGalleries(wdOutlineNumberGallery).ListTemplates(5).ListLevels(3) ' 设置第3级标题编号样式
        .NumberFormat = "第%3条" ' 编号格式为“第X条”
        .NumberStyle = wdListNumberStyleArabic ' 阿拉伯数字编号样式
        .NumberPosition = CentimetersToPoints(0) ' 编号左缩进0厘米
        .Alignment = wdListLevelAlignLeft ' 编号左对齐
        .TextPosition = CentimetersToPoints(1.48) ' 标题文本缩进1.48厘米（4字符）
        .TabPosition = wdUndefined ' 制表位未定义
        .ResetOnHigher = 0 ' 不受更高级别影响
        .StartAt = 1 ' 从1开始编号
        .LinkedStyle = "标题 3" ' 关联到“标题 3”样式
    End With
    With ActiveDocument.Styles("标题 3").Font ' 设置标题3字体
        .NameFarEast = "宋体" ' 中文字体为宋体
        .NameAscii = "宋体" ' 英文及数字字体为宋体
        .NameOther = "宋体" ' 其他语言字体为宋体
        .Name = "宋体" ' 字体名称
        .Size = 12 ' 字号12磅
        .Bold = False ' 不加粗
        .Color = wdColorBlack ' 字体颜色黑色
    End With
    With ActiveDocument.Styles("标题 3").ParagraphFormat ' 设置标题3段落格式
        .Alignment = wdAlignParagraphLeft ' 段落左对齐
        .LeftIndent = CentimetersToPoints(1.48) ' 左缩进1.48厘米（4字符）
        .FirstLineIndent = -CentimetersToPoints(1.48) ' 悬挂缩进1.48厘米
        .SpaceBefore = 6 ' 段前6磅
        .SpaceAfter = 6 ' 段后6磅
    End With
    ' ====================标题4：编号+样式设置====================
    With ListGalleries(wdOutlineNumberGallery).ListTemplates(5).ListLevels(4) ' 设置第5级标题编号样式
        .NumberFormat = "(%4)" ' 编号格式为“(X)”
        .NumberStyle = wdListNumberStyleArabic ' 阿拉伯数字编号样式
        .NumberPosition = CentimetersToPoints(1.48) ' 编号左缩进1.48厘米
        .Alignment = wdListLevelAlignLeft ' 编号左对齐
        .TextPosition = CentimetersToPoints(1.48) ' 标题文本缩进1.48厘米
        .TabPosition = wdUndefined ' 制表位未定义
        .ResetOnHigher = 0 ' 不受更高级别影响
        .StartAt = 1 ' 从1开始编号
        .LinkedStyle = "标题 4" ' 关联到“标题 5”样式
    End With
    With ActiveDocument.Styles("标题 4").Font ' 设置标题5字体
        .NameFarEast = "宋体" ' 中文字体为宋体
        .NameAscii = "宋体" ' 英文及数字字体为宋体
        .NameOther = "宋体" ' 其他语言字体为宋体
        .Name = "宋体" ' 字体名称
        .Size = 12 ' 字号12磅
        .Bold = False ' 不加粗
        .Color = wdColorBlack ' 字体颜色黑色
    End With
    With ActiveDocument.Styles("标题 4").ParagraphFormat ' 设置标题4段落格式
        .Alignment = wdAlignParagraphLeft ' 段落左对齐
        .LeftIndent = CentimetersToPoints(1.48) ' 左缩进1.48厘米
        .FirstLineIndent = -CentimetersToPoints(1.48) ' 悬挂缩进1.48厘米
        .SpaceBefore = 6 ' 段前6磅
        .SpaceAfter = 6 ' 段后6磅
    End With
    ' ====================标题5：编号+样式设置====================
    With ListGalleries(wdOutlineNumberGallery).ListTemplates(5).ListLevels(5) ' 设置第5级标题编号样式
        .NumberFormat = "%5." ' 编号格式为“a.”
        .NumberStyle = wdListNumberStyleLowercaseLetter ' 小写字母编号样式
        .NumberPosition = CentimetersToPoints(1.85) ' 编号左缩进1.85厘米（5字符）
        .Alignment = wdListLevelAlignLeft ' 编号左对齐
        .TextPosition = CentimetersToPoints(1.85) ' 标题文本缩进1.85厘米
        .TabPosition = wdUndefined ' 制表位未定义
        .ResetOnHigher = 0 ' 不受更高级别影响
        .StartAt = 1 ' 从a开始编号
        .LinkedStyle = "标题 5" ' 关联到“标题 6”样式
    End With
    With ActiveDocument.Styles("标题 5").Font ' 设置标题6字体
        .NameFarEast = "宋体" ' 中文字体为宋体
        .NameAscii = "宋体" ' 英文及数字字体为宋体
        .NameOther = "宋体" ' 其他语言字体为宋体
        .Name = "宋体" ' 字体名称
        .Size = 12 ' 字号12磅
        .Bold = False ' 不加粗
        .Color = wdColorBlack ' 字体颜色黑色
    End With
    With ActiveDocument.Styles("标题 5").ParagraphFormat ' 设置标题6段落格式
        .Alignment = wdAlignParagraphLeft ' 段落左对齐
        .LeftIndent = CentimetersToPoints(1.85) ' 左缩进1.85厘米
        .FirstLineIndent = -CentimetersToPoints(1.85) ' 悬挂缩进1.85厘米
        .SpaceBefore = 0 ' 段前0磅
        .SpaceAfter = 6 ' 段后6磅
    End With
    ' ========== 标题6 标题7 标题8 标题9==========
    Dim i As Integer ' 定义循环变量
    For i = 6 To 9 ' 循环设置6~9级标题编号样式
        With ListGalleries(wdOutlineNumberGallery).ListTemplates(5).ListLevels(i) ' 设置第i级标题编号样式
            .NumberFormat = "" ' 不设置编号格式
            .NumberStyle = wdListNumberStyleArabic ' 阿拉伯数字编号样式
            .NumberPosition = CentimetersToPoints(0) ' 编号左缩进0厘米
            .TextPosition = CentimetersToPoints(1.48) ' 标题文本缩进1.48厘米
        End With
        With ActiveDocument.Styles("标题 " & i).ParagraphFormat ' 设置第i级标题段落格式
            .LeftIndent = CentimetersToPoints(1.48) ' 左缩进1.48厘米
            .FirstLineIndent = -CentimetersToPoints(1.48) ' 悬挂缩进1.48厘米
        End With
    Next i
    ' 设置模板名称为空，避免影响
    ListGalleries(wdOutlineNumberGallery).ListTemplates(5).Name = "" ' 清空模板名称
    ' 应用设置好的多级列表模板到选区
    Selection.Range.ListFormat.ApplyListTemplateWithLevel ListTemplate:= _
        ListGalleries(wdOutlineNumberGallery).ListTemplates(5), _
        ContinuePreviousList:=True, ApplyTo:=wdListApplyToWholeList, _
        DefaultListBehavior:=wdWord10ListBehavior ' 应用多级列表模板到选区

    ' ========== 设置快捷键 ==========
    Call SetHeadingShortcuts
    
    ' ========== 正文样式设置 ==========
    With ActiveDocument.Styles("正文").Font ' 设置正文字体
        .NameFarEast = "宋体" ' 中文字体宋体
        .NameAscii = "宋体" ' 英文及数字字体宋体
        .NameOther = "宋体" ' 其他语言字体宋体
        .Name = "宋体" ' 字体名称
        .Size = 12 ' 字号12磅（小四号）
        .Bold = False ' 不加粗
        .Color = wdColorBlack ' 黑色
    End With
    With ActiveDocument.Styles("正文").ParagraphFormat ' 设置正文段落格式
        .Alignment = wdAlignParagraphLeft ' 段落左对齐
        .SpaceBefore = 0 ' 段前0磅
        .SpaceAfter = 6 ' 段后6磅
        .LineSpacingRule = wdLineSpaceMultiple ' 多倍行距
        .LineSpacing = 1.2 ' 1.2倍行距
        .FirstLineIndent = CentimetersToPoints(0) ' 首行缩进0厘米
        .LeftIndent = CentimetersToPoints(1.48) ' 左缩进1.48厘米（4字符）
        .RightIndent = 0 ' 右缩进0
    End With

    ' ========== 无间隔样式设置 ==========
    With ActiveDocument.Styles("无间隔").Font ' 设置无间隔样式字体
        .NameFarEast = "宋体" ' 中文字体宋体
        .NameAscii = "宋体" ' 英文及数字字体宋体
        .NameOther = "宋体" ' 其他语言字体宋体
        .Name = "宋体" ' 字体名称
        .Size = 12 ' 字号12磅（小四号）
        .Bold = False ' 不加粗
        .Color = wdColorBlack ' 黑色
    End With
    With ActiveDocument.Styles("无间隔").ParagraphFormat ' 设置无间隔样式段落格式
        .Alignment = wdAlignParagraphCenter ' 段落居中对齐
    End With

    ' 显示设置完成消息
    MsgBox "法律条文样式和快捷键设置完成！" & vbCrLf & vbCrLf & _
           "编号格式：" & vbCrLf & _
           "标题1: 第一章、第二章..." & vbCrLf & _
           "标题2: 第一节、第二节、..." & vbCrLf & _
           "标题3: 第1条、第2条..." & vbCrLf & _
           "标题4: (1)、(2)、(3)..." & vbCrLf & _
           "标题5: a.、b.、c...." & vbCrLf & vbCrLf & _
           "快捷键：" & vbCrLf & _
           "Alt+1 = 标题 1" & vbCrLf & _
           "Alt+2 = 标题 2" & vbCrLf & _
           "Alt+3 = 标题 3" & vbCrLf & _
           "Alt+4 = 标题 4" & vbCrLf & _
           "Alt+5 = 标题 5" & vbCrLf & _
           "Alt+` = 正文", vbInformation, "设置完成"

End Sub ' 过程结束

' ========== 设置标题快捷键函数 ==========
Sub SetHeadingShortcuts()
    ' 设置自定义上下文为当前文档的模板
    CustomizationContext = ActiveDocument.AttachedTemplate

    ' 错误处理：如果快捷键已存在，先清除再重新设置
    On Error Resume Next

    ' 清除可能存在的旧快捷键绑定
    Dim kb As KeyBinding
    Set kb = KeyBindings.Key(BuildKeyCode(wdKeyAlt, wdKey1))
    If Not kb Is Nothing Then kb.Clear
    Set kb = KeyBindings.Key(BuildKeyCode(wdKeyAlt, wdKey2))
    If Not kb Is Nothing Then kb.Clear
    Set kb = KeyBindings.Key(BuildKeyCode(wdKeyAlt, wdKey3))
    If Not kb Is Nothing Then kb.Clear
    Set kb = KeyBindings.Key(BuildKeyCode(wdKeyAlt, wdKey4))
    If Not kb Is Nothing Then kb.Clear
    Set kb = KeyBindings.Key(BuildKeyCode(wdKeyAlt, wdKey5))
    If Not kb Is Nothing Then kb.Clear
    ' 清除正文样式的旧快捷键绑定 Alt+`
    Set kb = KeyBindings.Key(BuildKeyCode(wdKeyAlt, 192))
    If Not kb Is Nothing Then kb.Clear

    ' 设置新的快捷键绑定
    KeyBindings.Add KeyCode:=BuildKeyCode(wdKeyAlt, wdKey1), KeyCategory:=wdKeyCategoryStyle, Command:="标题 1"
    KeyBindings.Add KeyCode:=BuildKeyCode(wdKeyAlt, wdKey2), KeyCategory:=wdKeyCategoryStyle, Command:="标题 2"
    KeyBindings.Add KeyCode:=BuildKeyCode(wdKeyAlt, wdKey3), KeyCategory:=wdKeyCategoryStyle, Command:="标题 3"
    KeyBindings.Add KeyCode:=BuildKeyCode(wdKeyAlt, wdKey4), KeyCategory:=wdKeyCategoryStyle, Command:="标题 4"
    KeyBindings.Add KeyCode:=BuildKeyCode(wdKeyAlt, wdKey5), KeyCategory:=wdKeyCategoryStyle, Command:="标题 5"
    ' 添加正文样式快捷键 Alt+`（反引号键）
    KeyBindings.Add KeyCode:=BuildKeyCode(wdKeyAlt, 192), KeyCategory:=wdKeyCategoryStyle, Command:="正文"

    On Error GoTo 0
End Sub
