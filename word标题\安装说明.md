# Word标题格式插件安装说明

## 插件功能
自动设置Word文档的中文多级标题编号格式：
- 标题1：第一章、第二章...
- 标题2：一、二、三...
- 标题3：（一）、（二）...
- 标题4：1.、2.、3...
- 标题5：(1)、(2)...
- 标题6：a.、b.、c...

## 方案一：Word加载项（推荐）

### 安装步骤

#### 1. 创建模板文件
1. 打开Word，新建空白文档
2. 按 `Alt + F11` 打开VBA编辑器
3. 在VBA编辑器中，插入 → 模块
4. 将 `TitleFormatModule.bas` 中的代码复制到模块中
5. 保存文件为 `标题格式插件.dotm`（Word启用宏的模板格式）

#### 2. 设置为加载项
1. 在Word中，文件 → 选项 → 加载项
2. 在"管理"下拉框中选择"Word加载项"，点击"转到"
3. 点击"添加"，选择刚才保存的 `标题格式插件.dotm` 文件
4. 确保该加载项被勾选，点击"确定"

#### 3. 使用插件
- 插件加载后会在功能区添加"标题格式"选项卡
- 点击"应用标题格式"按钮即可设置标题编号
- 使用快速样式按钮快速应用标题样式

### 自定义功能区（可选）
如果要添加自定义功能区界面：
1. 使用Office Custom UI Editor工具
2. 打开 `标题格式插件.dotm` 文件
3. 插入 `RibbonUI.xml` 内容
4. 保存并重新加载插件

## 方案二：个人宏工作簿

### 安装步骤
1. 按 `Alt + F11` 打开VBA编辑器
2. 在左侧项目窗口中找到"Normal"或"Personal.xlsb"
3. 右键点击 → 插入 → 模块
4. 将VBA代码复制到模块中
5. 保存（Ctrl + S）

### 创建快捷按钮
1. 文件 → 选项 → 自定义功能区
2. 在"从下列位置选择命令"中选择"宏"
3. 找到 `ChangeParagraphStyle` 宏
4. 添加到自定义组中

## 方案三：快速访问工具栏

### 设置步骤
1. 点击快速访问工具栏右侧的下拉箭头
2. 选择"其他命令"
3. 在"从下列位置选择命令"中选择"宏"
4. 选择 `ChangeParagraphStyle` 宏，点击"添加"
5. 可以修改显示名称和图标

## 使用说明

### 基本使用
1. 打开需要设置标题格式的Word文档
2. 运行插件（点击按钮或运行宏）
3. 插件会自动设置所有标题级别的编号格式和样式

### 应用标题样式
- 选中文本后，在"开始"选项卡中选择对应的标题样式
- 或使用插件提供的快速样式按钮
- 标题会自动应用相应的编号格式

### 注意事项
1. 建议在文档开始编写前就设置标题格式
2. 如果文档已有内容，设置后可能需要手动调整部分格式
3. 插件会覆盖现有的标题编号设置
4. 支持Word 2010及以上版本

## 故障排除

### 常见问题
1. **插件无法加载**
   - 检查宏安全设置：文件 → 选项 → 信任中心 → 宏设置
   - 选择"启用所有宏"或"禁用无数字签名的宏时发出通知"

2. **编号格式不正确**
   - 重新运行插件
   - 检查是否有其他模板影响

3. **功能区按钮不显示**
   - 重启Word
   - 检查加载项是否正确加载

### 卸载插件
1. 文件 → 选项 → 加载项
2. 选择要卸载的插件，点击"删除"
3. 或直接删除模板文件

## 技术支持
如有问题，请检查：
- Word版本兼容性
- 宏安全设置
- 模板文件路径

## 版本信息
- 版本：1.0
- 兼容性：Word 2010+
- 更新日期：2025年
