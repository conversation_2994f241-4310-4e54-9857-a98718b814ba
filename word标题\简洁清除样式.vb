' ========== 简洁样式清理工具 ==========
' 功能：清除所有自定义样式，只保留系统自带的基本样式
' 保留样式：标题1-9、正文、无间隔等系统默认样式
' 对所有段落应用的自定义样式，将其改为对应的标准样式后再删除重复样式，如有在标题1的基础上自定义了标题1其他，则将其改为标题1后再删除标题1其他。
' 如有在标题2的基础上自定义了标题2其他，则将其改为标题2后再删除标题2其他，依次类推。
' 如有在正文的基础上自定义了正文其他，则将其改为正文后再删除正文其他。
' 各级标题1、2、3、4、5、6、7、8、9等都只保留唯一的一个

Sub 简洁样式清理工具()
    Dim doc As Document
    Dim para As Paragraph
    Dim style As style
    Dim styleName As String
    Dim standardStyleName As String
    Dim i As Integer

    ' 获取当前文档
    Set doc = ActiveDocument

    ' 显示进度信息
    Application.ScreenUpdating = False
    StatusBar = "正在清理样式..."

    ' 第一步：将自定义样式替换为标准样式
    For Each para In doc.Paragraphs
        styleName = para.style.NameLocal
        standardStyleName = GetStandardStyleName(styleName)

        ' 如果找到对应的标准样式，则替换
        If standardStyleName <> "" And standardStyleName <> styleName Then
            On Error Resume Next
            para.style = standardStyleName
            On Error GoTo 0
        End If
    Next para

    ' 第二步：删除自定义样式
    For i = doc.Styles.Count To 1 Step -1
        Set style = doc.Styles(i)

        ' 检查是否为可删除的自定义样式
        If IsCustomStyle(style) Then
            On Error Resume Next
            style.Delete
            On Error GoTo 0
        End If
    Next i

    ' 恢复屏幕更新
    Application.ScreenUpdating = True
    StatusBar = "样式清理完成！"

    ' 显示完成消息
    MsgBox "样式清理完成！" & vbCrLf & _
           "已保留系统默认样式，删除了所有自定义样式。", _
           vbInformation, "简洁样式清理工具"
End Sub

' 获取对应的标准样式名称
Function GetStandardStyleName(styleName As String) As String
    Dim standardName As String
    standardName = ""

    ' 更精确的标题样式映射 - 使用正则表达式模式匹配
    ' 匹配包含"标题1"、"标题 1"、"Heading 1"等变体的样式
    If IsHeadingVariant(styleName, "1") Then
        standardName = "标题 1"
    ElseIf IsHeadingVariant(styleName, "2") Then
        standardName = "标题 2"
    ElseIf IsHeadingVariant(styleName, "3") Then
        standardName = "标题 3"
    ElseIf IsHeadingVariant(styleName, "4") Then
        standardName = "标题 4"
    ElseIf IsHeadingVariant(styleName, "5") Then
        standardName = "标题 5"
    ElseIf IsHeadingVariant(styleName, "6") Then
        standardName = "标题 6"
    ElseIf IsHeadingVariant(styleName, "7") Then
        standardName = "标题 7"
    ElseIf IsHeadingVariant(styleName, "8") Then
        standardName = "标题 8"
    ElseIf IsHeadingVariant(styleName, "9") Then
        standardName = "标题 9"
    ' 正文样式映射 - 更精确匹配
    ElseIf IsNormalVariant(styleName) Then
        standardName = "正文"
    End If

    GetStandardStyleName = standardName
End Function

' 判断是否为标题变体样式
Function IsHeadingVariant(styleName As String, level As String) As Boolean
    Dim lowerStyleName As String
    lowerStyleName = LCase(styleName)

    ' 检查各种标题变体模式
    If InStr(lowerStyleName, "标题" & level) > 0 Or _
       InStr(lowerStyleName, "标题 " & level) > 0 Or _
       InStr(lowerStyleName, "heading " & level) > 0 Or _
       InStr(lowerStyleName, "heading" & level) > 0 Then
        ' 确保不是标准样式本身
        If styleName <> "标题 " & level And styleName <> "Heading " & level Then
            IsHeadingVariant = True
        Else
            IsHeadingVariant = False
        End If
    Else
        IsHeadingVariant = False
    End If
End Function

' 判断是否为正文变体样式
Function IsNormalVariant(styleName As String) As Boolean
    Dim lowerStyleName As String
    lowerStyleName = LCase(styleName)

    ' 检查正文变体模式
    If (InStr(lowerStyleName, "正文") > 0 Or InStr(lowerStyleName, "normal") > 0) And _
       styleName <> "正文" And styleName <> "Normal" Then
        IsNormalVariant = True
    Else
        IsNormalVariant = False
    End If
End Function

' 判断是否为可删除的自定义样式
Function IsCustomStyle(style As style) As Boolean
    Dim styleName As String
    Dim isBuiltIn As Boolean

    styleName = style.NameLocal
    isBuiltIn = style.BuiltIn

    ' 扩展的系统内置样式列表（保留的样式）
    Dim preservedStyles As Variant
    preservedStyles = Array( _
        "正文", "Normal", _
        "标题 1", "标题 2", "标题 3", "标题 4", "标题 5", _
        "标题 6", "标题 7", "标题 8", "标题 9", _
        "Heading 1", "Heading 2", "Heading 3", "Heading 4", "Heading 5", _
        "Heading 6", "Heading 7", "Heading 8", "Heading 9", _
        "无间隔", "No Spacing", "无间距", _
        "页眉", "页脚", "Header", "Footer", _
        "目录 1", "目录 2", "目录 3", "TOC 1", "TOC 2", "TOC 3", _
        "超链接", "Hyperlink", _
        "强调", "Emphasis", _
        "加强", "Strong", _
        "引用", "Quote", _
        "标题", "Title", _
        "副标题", "Subtitle", _
        "列表段落", "List Paragraph", _
        "批注文本", "Comment Text", "批注主题", "Comment Subject", _
        "气球文字", "Balloon Text", _
        "脚注文本", "Footnote Text", "尾注文本", "Endnote Text", _
        "文档结构图", "Document Map", _
        "普通表格", "Table Normal", "表格网格", "Table Grid", _
        "Default Paragraph Font", "默认段落字体" _
    )

    ' 首先检查是否在保留列表中
    Dim i As Integer
    For i = 0 To UBound(preservedStyles)
        If styleName = preservedStyles(i) Then
            IsCustomStyle = False
            Exit Function
        End If
    Next i

    ' 检查是否为明显的自定义样式（包含特定关键词的变体）
    If IsObviousCustomStyle(styleName) Then
        IsCustomStyle = True
        Exit Function
    End If

    ' 对于内置样式，需要更谨慎的判断
    If isBuiltIn Then
        ' 保留大部分内置样式，除非明确知道是可删除的
        If IsKnownDeletableBuiltInStyle(styleName) Then
            IsCustomStyle = True
        Else
            IsCustomStyle = False
        End If
    Else
        ' 非内置样式通常是自定义样式，可以删除
        IsCustomStyle = True
    End If
End Function

' 判断是否为明显的自定义样式
Function IsObviousCustomStyle(styleName As String) As Boolean
    Dim lowerStyleName As String
    lowerStyleName = LCase(styleName)

    ' 检查是否包含明显的自定义样式特征
    ' 1. 包含字体名称的样式（如"宋体"、"黑体"等）
    If InStr(lowerStyleName, "宋体") > 0 Or InStr(lowerStyleName, "黑体") > 0 Or _
       InStr(lowerStyleName, "楷体") > 0 Or InStr(lowerStyleName, "仿宋") > 0 Or _
       InStr(lowerStyleName, "微软雅黑") > 0 Or InStr(lowerStyleName, "times") > 0 Or _
       InStr(lowerStyleName, "arial") > 0 Then
        IsObviousCustomStyle = True
        Exit Function
    End If

    ' 2. 包含格式描述的样式（如"段前"、"段后"、"行距"等）
    If InStr(lowerStyleName, "段前") > 0 Or InStr(lowerStyleName, "段后") > 0 Or _
       InStr(lowerStyleName, "行距") > 0 Or InStr(lowerStyleName, "首行") > 0 Or _
       InStr(lowerStyleName, "缩进") > 0 Then
        IsObviousCustomStyle = True
        Exit Function
    End If

    ' 3. 包含明显自定义标识的样式
    If InStr(lowerStyleName, "自定义") > 0 Or InStr(lowerStyleName, "custom") > 0 Or _
       InStr(lowerStyleName, "用户") > 0 Or InStr(lowerStyleName, "user") > 0 Then
        IsObviousCustomStyle = True
        Exit Function
    End If

    ' 4. 标题或正文的变体（但不是标准样式）
    If (InStr(lowerStyleName, "标题") > 0 And styleName <> "标题" And _
        Not IsStandardHeading(styleName)) Or _
       (InStr(lowerStyleName, "正文") > 0 And styleName <> "正文") Then
        IsObviousCustomStyle = True
        Exit Function
    End If

    IsObviousCustomStyle = False
End Function

' 判断是否为标准标题样式
Function IsStandardHeading(styleName As String) As Boolean
    Dim standardHeadings As Variant
    standardHeadings = Array("标题 1", "标题 2", "标题 3", "标题 4", "标题 5", _
                            "标题 6", "标题 7", "标题 8", "标题 9", _
                            "Heading 1", "Heading 2", "Heading 3", "Heading 4", "Heading 5", _
                            "Heading 6", "Heading 7", "Heading 8", "Heading 9")

    Dim i As Integer
    For i = 0 To UBound(standardHeadings)
        If styleName = standardHeadings(i) Then
            IsStandardHeading = True
            Exit Function
        End If
    Next i

    IsStandardHeading = False
End Function

' 判断是否为已知可删除的内置样式
Function IsKnownDeletableBuiltInStyle(styleName As String) As Boolean
    ' 这里可以列出一些已知的、通常不需要的内置样式
    ' 需要根据实际情况谨慎添加

    ' 暂时保守处理，不删除任何内置样式
    IsKnownDeletableBuiltInStyle = False
End Function

