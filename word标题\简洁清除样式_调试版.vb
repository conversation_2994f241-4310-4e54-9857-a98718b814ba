' ========== 简洁样式清理工具（调试版） ==========
' 功能：清除所有自定义样式，只保留系统自带的基本样式
' 调试版本：会显示详细的样式分析信息，不会实际删除样式

Sub 简洁样式清理工具_调试版()
    Dim doc As Document
    Dim para As Paragraph
    Dim style As style
    Dim styleName As String
    Dim standardStyleName As String
    Dim i As Integer
    Dim debugInfo As String
    Dim customStyleCount As Integer
    Dim preservedStyleCount As Integer

    ' 获取当前文档
    Set doc = ActiveDocument

    ' 显示进度信息
    Application.ScreenUpdating = False
    StatusBar = "正在分析样式..."

    debugInfo = "=== 样式分析报告 ===" & vbCrLf & vbCrLf
    customStyleCount = 0
    preservedStyleCount = 0

    ' 分析所有样式
    debugInfo = debugInfo & "文档中的所有样式：" & vbCrLf
    For i = 1 To doc.Styles.Count
        Set style = doc.Styles(i)
        styleName = style.NameLocal
        
        If IsCustomStyle(style) Then
            debugInfo = debugInfo & "【可删除】" & styleName
            If Not style.BuiltIn Then
                debugInfo = debugInfo & " (用户自定义)"
            Else
                debugInfo = debugInfo & " (内置但可删除)"
            End If
            debugInfo = debugInfo & vbCrLf
            customStyleCount = customStyleCount + 1
        Else
            debugInfo = debugInfo & "【保留】" & styleName
            If style.BuiltIn Then
                debugInfo = debugInfo & " (系统内置)"
            Else
                debugInfo = debugInfo & " (用户创建但保留)"
            End If
            debugInfo = debugInfo & vbCrLf
            preservedStyleCount = preservedStyleCount + 1
        End If
    Next i

    ' 分析段落样式使用情况
    debugInfo = debugInfo & vbCrLf & "=== 段落样式使用情况 ===" & vbCrLf
    Dim styleUsage As Object
    Set styleUsage = CreateObject("Scripting.Dictionary")
    
    For Each para In doc.Paragraphs
        styleName = para.style.NameLocal
        If styleUsage.Exists(styleName) Then
            styleUsage(styleName) = styleUsage(styleName) + 1
        Else
            styleUsage.Add styleName, 1
        End If
    Next para
    
    Dim key As Variant
    For Each key In styleUsage.Keys
        standardStyleName = GetStandardStyleName(CStr(key))
        debugInfo = debugInfo & "样式：" & key & " (使用次数：" & styleUsage(key) & ")"
        If standardStyleName <> "" Then
            debugInfo = debugInfo & " → 将转换为：" & standardStyleName
        End If
        debugInfo = debugInfo & vbCrLf
    Next key

    ' 统计信息
    debugInfo = debugInfo & vbCrLf & "=== 统计信息 ===" & vbCrLf
    debugInfo = debugInfo & "总样式数：" & doc.Styles.Count & vbCrLf
    debugInfo = debugInfo & "将保留：" & preservedStyleCount & " 个样式" & vbCrLf
    debugInfo = debugInfo & "将删除：" & customStyleCount & " 个样式" & vbCrLf

    ' 恢复屏幕更新
    Application.ScreenUpdating = True
    StatusBar = "样式分析完成！"

    ' 显示分析结果
    MsgBox debugInfo, vbInformation, "样式分析报告"
End Sub

' 获取对应的标准样式名称
Function GetStandardStyleName(styleName As String) As String
    Dim standardName As String
    standardName = ""

    ' 更精确的标题样式映射
    If IsHeadingVariant(styleName, "1") Then
        standardName = "标题 1"
    ElseIf IsHeadingVariant(styleName, "2") Then
        standardName = "标题 2"
    ElseIf IsHeadingVariant(styleName, "3") Then
        standardName = "标题 3"
    ElseIf IsHeadingVariant(styleName, "4") Then
        standardName = "标题 4"
    ElseIf IsHeadingVariant(styleName, "5") Then
        standardName = "标题 5"
    ElseIf IsHeadingVariant(styleName, "6") Then
        standardName = "标题 6"
    ElseIf IsHeadingVariant(styleName, "7") Then
        standardName = "标题 7"
    ElseIf IsHeadingVariant(styleName, "8") Then
        standardName = "标题 8"
    ElseIf IsHeadingVariant(styleName, "9") Then
        standardName = "标题 9"
    ' 正文样式映射
    ElseIf IsNormalVariant(styleName) Then
        standardName = "正文"
    End If

    GetStandardStyleName = standardName
End Function

' 判断是否为标题变体样式
Function IsHeadingVariant(styleName As String, level As String) As Boolean
    Dim lowerStyleName As String
    lowerStyleName = LCase(styleName)

    ' 检查各种标题变体模式
    If InStr(lowerStyleName, "标题" & level) > 0 Or _
       InStr(lowerStyleName, "标题 " & level) > 0 Or _
       InStr(lowerStyleName, "heading " & level) > 0 Or _
       InStr(lowerStyleName, "heading" & level) > 0 Then
        ' 确保不是标准样式本身
        If styleName <> "标题 " & level And styleName <> "Heading " & level Then
            IsHeadingVariant = True
        Else
            IsHeadingVariant = False
        End If
    Else
        IsHeadingVariant = False
    End If
End Function

' 判断是否为正文变体样式
Function IsNormalVariant(styleName As String) As Boolean
    Dim lowerStyleName As String
    lowerStyleName = LCase(styleName)

    ' 检查正文变体模式
    If (InStr(lowerStyleName, "正文") > 0 Or InStr(lowerStyleName, "normal") > 0) And _
       styleName <> "正文" And styleName <> "Normal" Then
        IsNormalVariant = True
    Else
        IsNormalVariant = False
    End If
End Function

' 判断是否为可删除的自定义样式
Function IsCustomStyle(style As style) As Boolean
    Dim styleName As String
    Dim isBuiltIn As Boolean

    styleName = style.NameLocal
    isBuiltIn = style.BuiltIn

    ' 扩展的系统内置样式列表（保留的样式）
    Dim preservedStyles As Variant
    preservedStyles = Array( _
        "正文", "Normal", _
        "标题 1", "标题 2", "标题 3", "标题 4", "标题 5", _
        "标题 6", "标题 7", "标题 8", "标题 9", _
        "Heading 1", "Heading 2", "Heading 3", "Heading 4", "Heading 5", _
        "Heading 6", "Heading 7", "Heading 8", "Heading 9", _
        "无间隔", "No Spacing", "无间距", _
        "页眉", "页脚", "Header", "Footer", _
        "目录 1", "目录 2", "目录 3", "TOC 1", "TOC 2", "TOC 3", _
        "超链接", "Hyperlink", _
        "强调", "Emphasis", _
        "加强", "Strong", _
        "引用", "Quote", _
        "标题", "Title", _
        "副标题", "Subtitle", _
        "列表段落", "List Paragraph", _
        "批注文本", "Comment Text", "批注主题", "Comment Subject", _
        "气球文字", "Balloon Text", _
        "脚注文本", "Footnote Text", "尾注文本", "Endnote Text", _
        "文档结构图", "Document Map", _
        "普通表格", "Table Normal", "表格网格", "Table Grid", _
        "Default Paragraph Font", "默认段落字体" _
    )

    ' 首先检查是否在保留列表中
    Dim i As Integer
    For i = 0 To UBound(preservedStyles)
        If styleName = preservedStyles(i) Then
            IsCustomStyle = False
            Exit Function
        End If
    Next i

    ' 检查是否为明显的自定义样式
    If IsObviousCustomStyle(styleName) Then
        IsCustomStyle = True
        Exit Function
    End If

    ' 对于内置样式，需要更谨慎的判断
    If isBuiltIn Then
        ' 保留大部分内置样式，除非明确知道是可删除的
        IsCustomStyle = False
    Else
        ' 非内置样式通常是自定义样式，可以删除
        IsCustomStyle = True
    End If
End Function

' 判断是否为明显的自定义样式
Function IsObviousCustomStyle(styleName As String) As Boolean
    Dim lowerStyleName As String
    lowerStyleName = LCase(styleName)
    
    ' 检查是否包含明显的自定义样式特征
    ' 1. 包含字体名称的样式
    If InStr(lowerStyleName, "宋体") > 0 Or InStr(lowerStyleName, "黑体") > 0 Or _
       InStr(lowerStyleName, "楷体") > 0 Or InStr(lowerStyleName, "仿宋") > 0 Or _
       InStr(lowerStyleName, "微软雅黑") > 0 Or InStr(lowerStyleName, "times") > 0 Or _
       InStr(lowerStyleName, "arial") > 0 Then
        IsObviousCustomStyle = True
        Exit Function
    End If
    
    ' 2. 包含格式描述的样式
    If InStr(lowerStyleName, "段前") > 0 Or InStr(lowerStyleName, "段后") > 0 Or _
       InStr(lowerStyleName, "行距") > 0 Or InStr(lowerStyleName, "首行") > 0 Or _
       InStr(lowerStyleName, "缩进") > 0 Then
        IsObviousCustomStyle = True
        Exit Function
    End If
    
    ' 3. 标题或正文的变体（但不是标准样式）
    If (InStr(lowerStyleName, "标题") > 0 And styleName <> "标题" And _
        Not IsStandardHeading(styleName)) Or _
       (InStr(lowerStyleName, "正文") > 0 And styleName <> "正文") Then
        IsObviousCustomStyle = True
        Exit Function
    End If
    
    IsObviousCustomStyle = False
End Function

' 判断是否为标准标题样式
Function IsStandardHeading(styleName As String) As Boolean
    Dim standardHeadings As Variant
    standardHeadings = Array("标题 1", "标题 2", "标题 3", "标题 4", "标题 5", _
                            "标题 6", "标题 7", "标题 8", "标题 9", _
                            "Heading 1", "Heading 2", "Heading 3", "Heading 4", "Heading 5", _
                            "Heading 6", "Heading 7", "Heading 8", "Heading 9")
    
    Dim i As Integer
    For i = 0 To UBound(standardHeadings)
        If styleName = standardHeadings(i) Then
            IsStandardHeading = True
            Exit Function
        End If
    Next i
    
    IsStandardHeading = False
End Function
