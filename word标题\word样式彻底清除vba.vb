' ========== word样式彻底清理工具 ==========
' 功能：清除所有自定义样式，只保留系统自带的基本样式
' 选非自定的自定义的标题1内容，清除格式，再将其改为自带标题；
' 选非自定的自定义的标题2内容，清除格式，再将其改为自带标题；
' 选非自定的自定义的标题3内容，清除格式，再将其改为自带标题；
' 选非自定的自定义的标题4内容，清除格式，再将其改为自带标题；
' 选非自定的自定义的标题5内容，清除格式，再将其改为自带标题；
' 选非自定的自定义的标题6内容，清除格式，再将其改为自带标题；
' 选非自定的自定义的标题7内容，清除格式，再将其改为自带标题；
' 选非自定的自定义的标题8内容，清除格式，再将其改为自带标题；
' 选非自定的自定义的标题9内容，清除格式，再将其改为自带标题；
' 选非自定义的正文内容，清除格式，再将其改为自带正文；
' 其他内容均全部清除格式，再改为正文样式。

Sub WordStyleCleaner()
    ' 主程序入口
    Dim doc As Document
    Set doc = ActiveDocument

    ' 显示进度信息
    Application.ScreenUpdating = False
    StatusBar = "正在清理样式..."

    ' 执行清理操作
    Call CleanCustomStyles(doc)
    Call ProcessHeadingStyles(doc)
    Call ProcessNormalText(doc)
    Call ProcessOtherContent(doc)
    Call DeleteCustomStyles(doc)

    ' 恢复屏幕更新
    Application.ScreenUpdating = True
    StatusBar = "样式清理完成！"

    MsgBox "Word样式清理完成！" & vbCrLf & _
           "已将所有内容转换为系统默认样式。", vbInformation, "清理完成"
End Sub

Sub CleanCustomStyles(doc As Document)
    ' 清理自定义样式的格式
    Dim para As Paragraph
    Dim rng As Range

    For Each para In doc.Paragraphs
        Set rng = para.Range
        rng.End = rng.End - 1 ' 排除段落标记

        ' 清除直接格式
        rng.ClearFormatting
    Next para
End Sub

Sub ProcessHeadingStyles(doc As Document)
    ' 处理标题样式（标题1-9）
    Dim para As Paragraph
    Dim styleName As String
    Dim targetStyle As String
    Dim i As Integer

    For Each para In doc.Paragraphs
        styleName = para.Style.NameLocal

        ' 检查是否为自定义标题样式
        For i = 1 To 9
            If InStr(styleName, "标题" & i) > 0 And Not IsBuiltInStyle(styleName, "标题 " & i) Then
                ' 清除格式并应用系统标题样式
                para.Range.ClearFormatting
                para.Style = "标题 " & i
                Exit For
            End If
        Next i
    Next para
End Sub

Sub ProcessNormalText(doc As Document)
    ' 处理正文样式
    Dim para As Paragraph
    Dim styleName As String

    For Each para In doc.Paragraphs
        styleName = para.Style.NameLocal

        ' 检查是否为自定义正文样式
        If InStr(styleName, "正文") > 0 And Not IsBuiltInStyle(styleName, "正文") Then
            ' 清除格式并应用系统正文样式
            para.Range.ClearFormatting
            para.Style = "正文"
        End If
    Next para
End Sub

Sub ProcessOtherContent(doc As Document)
    ' 处理其他内容，全部改为正文样式
    Dim para As Paragraph
    Dim styleName As String
    Dim isSystemStyle As Boolean

    For Each para In doc.Paragraphs
        styleName = para.Style.NameLocal
        isSystemStyle = False

        ' 检查是否为系统内置样式
        If IsBuiltInStyle(styleName, "正文") Or _
           IsBuiltInStyle(styleName, "标题 1") Or _
           IsBuiltInStyle(styleName, "标题 2") Or _
           IsBuiltInStyle(styleName, "标题 3") Or _
           IsBuiltInStyle(styleName, "标题 4") Or _
           IsBuiltInStyle(styleName, "标题 5") Or _
           IsBuiltInStyle(styleName, "标题 6") Or _
           IsBuiltInStyle(styleName, "标题 7") Or _
           IsBuiltInStyle(styleName, "标题 8") Or _
           IsBuiltInStyle(styleName, "标题 9") Then
            isSystemStyle = True
        End If

        ' 如果不是系统样式，则清除格式并改为正文
        If Not isSystemStyle Then
            para.Range.ClearFormatting
            para.Style = "正文"
        End If
    Next para
End Sub

Function IsBuiltInStyle(currentStyle As String, targetStyle As String) As Boolean
    ' 判断是否为系统内置样式
    IsBuiltInStyle = (currentStyle = targetStyle)
End Function

Sub DeleteCustomStyles(doc As Document)
    ' 删除所有自定义样式
    Dim style As style
    Dim stylesToDelete As Collection
    Set stylesToDelete = New Collection

    ' 收集需要删除的自定义样式
    For Each style In doc.Styles
        If Not style.BuiltIn Then
            stylesToDelete.Add style
        End If
    Next style

    ' 删除自定义样式
    Dim i As Integer
    For i = 1 To stylesToDelete.Count
        On Error Resume Next
        stylesToDelete(i).Delete
        On Error GoTo 0
    Next i
End Sub

' 快速清理函数（简化版本）
Sub QuickStyleClean()
    ' 快速清理所有格式，全部改为正文
    Dim doc As Document
    Set doc = ActiveDocument

    Application.ScreenUpdating = False

    ' 选择全文
    doc.Range.Select

    ' 清除所有格式
    Selection.ClearFormatting

    ' 应用正文样式
    Selection.Style = "正文"

    ' 删除自定义样式
    Call DeleteCustomStyles(doc)

    Application.ScreenUpdating = True

    MsgBox "快速样式清理完成！", vbInformation
End Sub