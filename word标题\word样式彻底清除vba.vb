' ========== word样式彻底清理工具 ==========
' 功能：清除所有自定义样式，只保留系统自带的基本样式
' 保留样式：标题1-9、正文、无间隔等系统默认样式
' 对所有段落应用的自定义样式，将其改为对应的标准样式后再删除重复样式，如有在标题1的基础上自定义了标题1其他，则将其改为标题1后再删除标题1其他。
' 如有在标题2的基础上自定义了标题2其他，则将其改为标题2后再删除标题2其他，依次类推。
' 如有在正文的基础上自定义了正文其他，则将其改为正文后再删除正文其他。
' 各级标题1、2、3、4、5、6、7、8、9等都只保留唯一的一个

Sub CleanWordStyles()
    Dim doc As Document
    Dim style As style
    Dim para As Paragraph
    Dim stylesToDelete As Collection
    Dim i As Integer
    Dim styleName As String
    Dim standardStyles As Collection

    ' 初始化
    Set doc = ActiveDocument
    Set stylesToDelete = New Collection
    Set standardStyles = New Collection

    ' 定义要保留的标准样式
    Call AddStandardStyles(standardStyles)

    ' 显示进度信息
    Application.ScreenUpdating = False
    StatusBar = "正在清理样式..."

    ' 第一步：替换段落中的自定义样式为标准样式
    Call ReplaceCustomStylesInParagraphs(doc, standardStyles)

    ' 第二步：收集需要删除的自定义样式
    For Each style In doc.Styles
        If Not IsStandardStyle(style.NameLocal, standardStyles) Then
            If style.Type = wdStyleTypeParagraph Or style.Type = wdStyleTypeCharacter Then
                On Error Resume Next
                stylesToDelete.Add style.NameLocal
                On Error GoTo 0
            End If
        End If
    Next style

    ' 第三步：删除自定义样式
    Call DeleteCustomStyles(doc, stylesToDelete)

    ' 完成清理
    Application.ScreenUpdating = True
    StatusBar = "样式清理完成！"

    MsgBox "样式清理完成！" & vbCrLf & _
           "已删除 " & stylesToDelete.Count & " 个自定义样式。" & vbCrLf & _
           "保留了系统标准样式。", vbInformation, "样式清理工具"
End Sub

' 添加标准样式到集合
Private Sub AddStandardStyles(standardStyles As Collection)
    ' 基本样式
    standardStyles.Add "正文"
    standardStyles.Add "Normal"
    standardStyles.Add "无间隔"
    standardStyles.Add "No Spacing"

    ' 标题样式
    Dim i As Integer
    For i = 1 To 9
        standardStyles.Add "标题 " & i
        standardStyles.Add "Heading " & i
        standardStyles.Add "标题" & i
    Next i

    ' 其他常用标准样式
    standardStyles.Add "标题"
    standardStyles.Add "Title"
    standardStyles.Add "副标题"
    standardStyles.Add "Subtitle"
    standardStyles.Add "引用"
    standardStyles.Add "Quote"
    standardStyles.Add "强调"
    standardStyles.Add "Emphasis"
    standardStyles.Add "列表段落"
    standardStyles.Add "List Paragraph"
    standardStyles.Add "页眉"
    standardStyles.Add "Header"
    standardStyles.Add "页脚"
    standardStyles.Add "Footer"
    standardStyles.Add "脚注文本"
    standardStyles.Add "Footnote Text"
    standardStyles.Add "尾注文本"
    standardStyles.Add "Endnote Text"
    standardStyles.Add "目录 1"
    standardStyles.Add "TOC 1"
    standardStyles.Add "目录 2"
    standardStyles.Add "TOC 2"
    standardStyles.Add "目录 3"
    standardStyles.Add "TOC 3"
    standardStyles.Add "超链接"
    standardStyles.Add "Hyperlink"
    standardStyles.Add "已访问的超链接"
    standardStyles.Add "FollowedHyperlink"
End Sub

' 检查是否为标准样式
Private Function IsStandardStyle(styleName As String, standardStyles As Collection) As Boolean
    Dim item As Variant
    IsStandardStyle = False

    For Each item In standardStyles
        If LCase(Trim(styleName)) = LCase(Trim(item)) Then
            IsStandardStyle = True
            Exit Function
        End If
    Next item
End Function

' 替换段落中的自定义样式
Private Sub ReplaceCustomStylesInParagraphs(doc As Document, standardStyles As Collection)
    Dim para As Paragraph
    Dim styleName As String
    Dim newStyleName As String

    For Each para In doc.Paragraphs
        styleName = para.style.NameLocal

        ' 如果不是标准样式，尝试映射到标准样式
        If Not IsStandardStyle(styleName, standardStyles) Then
            newStyleName = MapToStandardStyle(styleName)

            If newStyleName <> "" Then
                On Error Resume Next
                para.style = newStyleName
                On Error GoTo 0
            End If
        End If
    Next para
End Sub

' 将自定义样式映射到标准样式
Private Function MapToStandardStyle(customStyleName As String) As String
    Dim lowerStyleName As String
    lowerStyleName = LCase(Trim(customStyleName))

    ' 标题样式映射
    If InStr(lowerStyleName, "标题1") > 0 Or InStr(lowerStyleName, "heading1") > 0 Then
        MapToStandardStyle = "标题 1"
    ElseIf InStr(lowerStyleName, "标题2") > 0 Or InStr(lowerStyleName, "heading2") > 0 Then
        MapToStandardStyle = "标题 2"
    ElseIf InStr(lowerStyleName, "标题3") > 0 Or InStr(lowerStyleName, "heading3") > 0 Then
        MapToStandardStyle = "标题 3"
    ElseIf InStr(lowerStyleName, "标题4") > 0 Or InStr(lowerStyleName, "heading4") > 0 Then
        MapToStandardStyle = "标题 4"
    ElseIf InStr(lowerStyleName, "标题5") > 0 Or InStr(lowerStyleName, "heading5") > 0 Then
        MapToStandardStyle = "标题 5"
    ElseIf InStr(lowerStyleName, "标题6") > 0 Or InStr(lowerStyleName, "heading6") > 0 Then
        MapToStandardStyle = "标题 6"
    ElseIf InStr(lowerStyleName, "标题7") > 0 Or InStr(lowerStyleName, "heading7") > 0 Then
        MapToStandardStyle = "标题 7"
    ElseIf InStr(lowerStyleName, "标题8") > 0 Or InStr(lowerStyleName, "heading8") > 0 Then
        MapToStandardStyle = "标题 8"
    ElseIf InStr(lowerStyleName, "标题9") > 0 Or InStr(lowerStyleName, "heading9") > 0 Then
        MapToStandardStyle = "标题 9"
    ElseIf InStr(lowerStyleName, "标题") > 0 Or InStr(lowerStyleName, "heading") > 0 Then
        MapToStandardStyle = "标题 1"  ' 默认映射到标题1
    ElseIf InStr(lowerStyleName, "正文") > 0 Or InStr(lowerStyleName, "normal") > 0 Or InStr(lowerStyleName, "body") > 0 Then
        MapToStandardStyle = "正文"
    Else
        MapToStandardStyle = "正文"  ' 默认映射到正文
    End If
End Function

' 删除自定义样式
Private Sub DeleteCustomStyles(doc As Document, stylesToDelete As Collection)
    Dim i As Integer
    Dim styleName As String
    Dim deletedCount As Integer

    deletedCount = 0

    ' 逐个删除自定义样式
    For i = 1 To stylesToDelete.Count
        styleName = stylesToDelete(i)

        On Error Resume Next
        ' 尝试删除样式
        doc.Styles(styleName).Delete

        If Err.Number = 0 Then
            deletedCount = deletedCount + 1
        End If

        Err.Clear
        On Error GoTo 0
    Next i

    StatusBar = "已删除 " & deletedCount & " 个自定义样式"
End Sub

' 高级清理功能 - 清理所有格式并重置为标准样式
Sub AdvancedStyleClean()
    Dim doc As Document
    Dim para As Paragraph
    Dim response As Integer

    Set doc = ActiveDocument

    ' 确认对话框
    response = MsgBox("高级清理将会：" & vbCrLf & _
                     "1. 清除所有自定义样式" & vbCrLf & _
                     "2. 重置所有段落格式" & vbCrLf & _
                     "3. 保留基本的标题层级结构" & vbCrLf & vbCrLf & _
                     "此操作不可撤销，是否继续？", _
                     vbYesNo + vbQuestion, "高级样式清理")

    If response = vbNo Then Exit Sub

    Application.ScreenUpdating = False
    StatusBar = "正在执行高级清理..."

    ' 先执行基本清理
    Call CleanWordStyles

    ' 重置所有段落的直接格式
    For Each para In doc.Paragraphs
        ' 清除直接格式，保留样式
        para.Range.ClearFormatting

        ' 根据内容特征重新应用合适的样式
        Call ApplyAppropriateStyle(para)
    Next para

    Application.ScreenUpdating = True
    StatusBar = "高级清理完成！"

    MsgBox "高级样式清理完成！", vbInformation, "样式清理工具"
End Sub

' 根据段落内容应用合适的样式
Private Sub ApplyAppropriateStyle(para As Paragraph)
    Dim text As String
    Dim textLength As Integer

    text = Trim(para.Range.text)
    textLength = Len(text)

    ' 跳过空段落
    If textLength <= 1 Then Exit Sub

    ' 根据文本特征判断样式
    If textLength < 50 And Not InStr(text, "。") > 0 And Not InStr(text, ".") > 0 Then
        ' 短文本且无句号，可能是标题
        If InStr(LCase(text), "第") > 0 And InStr(LCase(text), "章") > 0 Then
            para.style = "标题 1"
        ElseIf textLength < 20 Then
            para.style = "标题 2"
        Else
            para.style = "标题 3"
        End If
    Else
        ' 长文本，应用正文样式
        para.style = "正文"
    End If
End Sub

' 快速清理 - 只删除明显的自定义样式
Sub QuickStyleClean()
    Dim doc As Document
    Dim style As style
    Dim stylesToDelete As Collection
    Dim deletedCount As Integer

    Set doc = ActiveDocument
    Set stylesToDelete = New Collection
    deletedCount = 0

    Application.ScreenUpdating = False
    StatusBar = "正在执行快速清理..."

    ' 收集明显的自定义样式（包含特定关键词的）
    For Each style In doc.Styles
        If IsObviousCustomStyle(style.NameLocal) Then
            On Error Resume Next
            stylesToDelete.Add style.NameLocal
            On Error GoTo 0
        End If
    Next style

    ' 删除收集到的样式
    Call DeleteCustomStyles(doc, stylesToDelete)

    Application.ScreenUpdating = True
    StatusBar = "快速清理完成！"

    MsgBox "快速清理完成！" & vbCrLf & _
           "已删除 " & stylesToDelete.Count & " 个明显的自定义样式。", _
           vbInformation, "样式清理工具"
End Sub

' 判断是否为明显的自定义样式
Private Function IsObviousCustomStyle(styleName As String) As Boolean
    Dim lowerStyleName As String
    lowerStyleName = LCase(Trim(styleName))

    IsObviousCustomStyle = False

    ' 包含数字的标题样式变体
    If (InStr(lowerStyleName, "标题") > 0 Or InStr(lowerStyleName, "heading") > 0) And _
       (InStr(lowerStyleName, "字符") > 0 Or InStr(lowerStyleName, "char") > 0 Or _
        InStr(lowerStyleName, "+") > 0 Or InStr(lowerStyleName, "缩进") > 0 Or _
        InStr(lowerStyleName, "间距") > 0) Then
        IsObviousCustomStyle = True
    End If

    ' 包含特定后缀的样式
    If InStr(lowerStyleName, " + ") > 0 Or _
       InStr(lowerStyleName, "字符") > 0 Or _
       InStr(lowerStyleName, " char") > 0 Or _
       Right(lowerStyleName, 1) = "1" Or Right(lowerStyleName, 1) = "2" Or _
       Right(lowerStyleName, 1) = "3" Or Right(lowerStyleName, 1) = "4" Then
        IsObviousCustomStyle = True
    End If
End Function

' 显示样式统计信息
Sub ShowStyleStatistics()
    Dim doc As Document
    Dim style As style
    Dim totalStyles As Integer
    Dim customStyles As Integer
    Dim standardStyles As Collection
    Dim styleInfo As String

    Set doc = ActiveDocument
    Set standardStyles = New Collection
    Call AddStandardStyles(standardStyles)

    totalStyles = 0
    customStyles = 0
    styleInfo = "=== 文档样式统计 ===" & vbCrLf & vbCrLf

    For Each style In doc.Styles
        If style.Type = wdStyleTypeParagraph Then
            totalStyles = totalStyles + 1
            If Not IsStandardStyle(style.NameLocal, standardStyles) Then
                customStyles = customStyles + 1
                styleInfo = styleInfo & "自定义: " & style.NameLocal & vbCrLf
            End If
        End If
    Next style

    styleInfo = "总样式数: " & totalStyles & vbCrLf & _
               "标准样式: " & (totalStyles - customStyles) & vbCrLf & _
               "自定义样式: " & customStyles & vbCrLf & vbCrLf & styleInfo

    MsgBox styleInfo, vbInformation, "样式统计信息"
End Sub