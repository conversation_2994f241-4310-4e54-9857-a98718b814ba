' ========== word样式彻底清理工具 ==========
' 功能：清除所有自定义样式，只保留系统自带的基本样式
' 选非自定的自定义的标题1内容，清除格式，再将其改为自带标题；
' 选非自定的自定义的标题2内容，清除格式，再将其改为自带标题；
' 选非自定的自定义的标题3内容，清除格式，再将其改为自带标题；
' 选非自定的自定义的标题4内容，清除格式，再将其改为自带标题；
' 选非自定的自定义的标题5内容，清除格式，再将其改为自带标题；
' 选非自定的自定义的标题6内容，清除格式，再将其改为自带标题；
' 选非自定的自定义的标题7内容，清除格式，再将其改为自带标题；
' 选非自定的自定义的标题8内容，清除格式，再将其改为自带标题；
' 选非自定的自定义的标题9内容，清除格式，再将其改为自带标题；
' 选非自定义的正文内容，清除格式，再将其改为自带正文；
' 其他内容均全部清除格式，再改为正文样式。

Sub WordStyleCleaner()
    ' 主程序入口 - 逐级样式清理
    Dim doc As Document
    Set doc = ActiveDocument

    ' 显示进度信息
    Application.ScreenUpdating = False
    StatusBar = "正在逐级清理样式..."

    ' 逐个处理各级样式
    Call ProcessHeading1Style(doc)
    Call ProcessHeading2Style(doc)
    Call ProcessHeading3Style(doc)
    Call ProcessHeading4Style(doc)
    Call ProcessHeading5Style(doc)
    Call ProcessHeading6Style(doc)
    Call ProcessHeading7Style(doc)
    Call ProcessHeading8Style(doc)
    Call ProcessHeading9Style(doc)
    Call ProcessNormalTextStyle(doc)
    Call ProcessOtherContentStyle(doc)
    Call DeleteCustomStyles(doc)

    ' 恢复屏幕更新
    Application.ScreenUpdating = True
    StatusBar = "逐级样式清理完成！"

    MsgBox "Word逐级样式清理完成！" & vbCrLf & _
           "已按级别清理并重新赋予格式。", vbInformation, "清理完成"
End Sub

Sub ProcessHeading1Style(doc As Document)
    ' 处理标题1样式
    Dim para As Paragraph
    Dim styleName As String
    Dim processedCount As Integer

    processedCount = 0
    StatusBar = "正在处理标题1样式..."

    For Each para In doc.Paragraphs
        styleName = para.Style.NameLocal

        ' 检查是否包含"标题1"但不是系统内置的"标题 1"
        If (InStr(styleName, "标题1") > 0 Or InStr(styleName, "Heading1") > 0) And styleName <> "标题 1" Then
            ' 清除格式
            para.Range.ClearFormatting
            ' 重新应用系统标题1样式
            para.Style = "标题 1"
            processedCount = processedCount + 1
        End If
    Next para

    Debug.Print "标题1样式处理完成，共处理 " & processedCount & " 个段落"
End Sub

Sub ProcessHeading2Style(doc As Document)
    ' 处理标题2样式
    Dim para As Paragraph
    Dim styleName As String
    Dim processedCount As Integer

    processedCount = 0
    StatusBar = "正在处理标题2样式..."

    For Each para In doc.Paragraphs
        styleName = para.Style.NameLocal

        If (InStr(styleName, "标题2") > 0 Or InStr(styleName, "Heading2") > 0) And styleName <> "标题 2" Then
            para.Range.ClearFormatting
            para.Style = "标题 2"
            processedCount = processedCount + 1
        End If
    Next para

    Debug.Print "标题2样式处理完成，共处理 " & processedCount & " 个段落"
End Sub

Sub ProcessHeading3Style(doc As Document)
    ' 处理标题3样式
    Dim para As Paragraph
    Dim styleName As String
    Dim processedCount As Integer

    processedCount = 0
    StatusBar = "正在处理标题3样式..."

    For Each para In doc.Paragraphs
        styleName = para.Style.NameLocal

        If (InStr(styleName, "标题3") > 0 Or InStr(styleName, "Heading3") > 0) And styleName <> "标题 3" Then
            para.Range.ClearFormatting
            para.Style = "标题 3"
            processedCount = processedCount + 1
        End If
    Next para

    Debug.Print "标题3样式处理完成，共处理 " & processedCount & " 个段落"
End Sub

Sub ProcessHeading4Style(doc As Document)
    ' 处理标题4样式
    Dim para As Paragraph
    Dim styleName As String
    Dim processedCount As Integer

    processedCount = 0
    StatusBar = "正在处理标题4样式..."

    For Each para In doc.Paragraphs
        styleName = para.Style.NameLocal

        If (InStr(styleName, "标题4") > 0 Or InStr(styleName, "Heading4") > 0) And styleName <> "标题 4" Then
            para.Range.ClearFormatting
            para.Style = "标题 4"
            processedCount = processedCount + 1
        End If
    Next para

    Debug.Print "标题4样式处理完成，共处理 " & processedCount & " 个段落"
End Sub

Sub ProcessHeading5Style(doc As Document)
    ' 处理标题5样式
    Dim para As Paragraph
    Dim styleName As String
    Dim processedCount As Integer

    processedCount = 0
    StatusBar = "正在处理标题5样式..."

    For Each para In doc.Paragraphs
        styleName = para.Style.NameLocal

        If (InStr(styleName, "标题5") > 0 Or InStr(styleName, "Heading5") > 0) And styleName <> "标题 5" Then
            para.Range.ClearFormatting
            para.Style = "标题 5"
            processedCount = processedCount + 1
        End If
    Next para

    Debug.Print "标题5样式处理完成，共处理 " & processedCount & " 个段落"
End Sub

Sub ProcessHeading6Style(doc As Document)
    ' 处理标题6样式
    Dim para As Paragraph
    Dim styleName As String
    Dim processedCount As Integer

    processedCount = 0
    StatusBar = "正在处理标题6样式..."

    For Each para In doc.Paragraphs
        styleName = para.Style.NameLocal

        If (InStr(styleName, "标题6") > 0 Or InStr(styleName, "Heading6") > 0) And styleName <> "标题 6" Then
            para.Range.ClearFormatting
            para.Style = "标题 6"
            processedCount = processedCount + 1
        End If
    Next para

    Debug.Print "标题6样式处理完成，共处理 " & processedCount & " 个段落"
End Sub

Sub ProcessHeading7Style(doc As Document)
    ' 处理标题7样式
    Dim para As Paragraph
    Dim styleName As String
    Dim processedCount As Integer

    processedCount = 0
    StatusBar = "正在处理标题7样式..."

    For Each para In doc.Paragraphs
        styleName = para.Style.NameLocal

        If (InStr(styleName, "标题7") > 0 Or InStr(styleName, "Heading7") > 0) And styleName <> "标题 7" Then
            para.Range.ClearFormatting
            para.Style = "标题 7"
            processedCount = processedCount + 1
        End If
    Next para

    Debug.Print "标题7样式处理完成，共处理 " & processedCount & " 个段落"
End Sub

Sub ProcessHeading8Style(doc As Document)
    ' 处理标题8样式
    Dim para As Paragraph
    Dim styleName As String
    Dim processedCount As Integer

    processedCount = 0
    StatusBar = "正在处理标题8样式..."

    For Each para In doc.Paragraphs
        styleName = para.Style.NameLocal

        If (InStr(styleName, "标题8") > 0 Or InStr(styleName, "Heading8") > 0) And styleName <> "标题 8" Then
            para.Range.ClearFormatting
            para.Style = "标题 8"
            processedCount = processedCount + 1
        End If
    Next para

    Debug.Print "标题8样式处理完成，共处理 " & processedCount & " 个段落"
End Sub

Sub ProcessHeading9Style(doc As Document)
    ' 处理标题9样式
    Dim para As Paragraph
    Dim styleName As String
    Dim processedCount As Integer

    processedCount = 0
    StatusBar = "正在处理标题9样式..."

    For Each para In doc.Paragraphs
        styleName = para.Style.NameLocal

        If (InStr(styleName, "标题9") > 0 Or InStr(styleName, "Heading9") > 0) And styleName <> "标题 9" Then
            para.Range.ClearFormatting
            para.Style = "标题 9"
            processedCount = processedCount + 1
        End If
    Next para

    Debug.Print "标题9样式处理完成，共处理 " & processedCount & " 个段落"
End Sub

Sub ProcessNormalTextStyle(doc As Document)
    ' 处理正文样式
    Dim para As Paragraph
    Dim styleName As String
    Dim processedCount As Integer

    processedCount = 0
    StatusBar = "正在处理正文样式..."

    For Each para In doc.Paragraphs
        styleName = para.Style.NameLocal

        ' 检查是否为自定义正文样式
        If (InStr(styleName, "正文") > 0 Or InStr(styleName, "Normal") > 0 Or InStr(styleName, "Body") > 0) And styleName <> "正文" Then
            para.Range.ClearFormatting
            para.Style = "正文"
            processedCount = processedCount + 1
        End If
    Next para

    Debug.Print "正文样式处理完成，共处理 " & processedCount & " 个段落"
End Sub

Sub ProcessOtherContentStyle(doc As Document)
    ' 处理其他内容样式
    Dim para As Paragraph
    Dim styleName As String
    Dim isSystemStyle As Boolean
    Dim processedCount As Integer

    processedCount = 0
    StatusBar = "正在处理其他内容样式..."

    For Each para In doc.Paragraphs
        styleName = para.Style.NameLocal
        isSystemStyle = False

        ' 检查是否为系统内置样式
        If styleName = "正文" Or _
           styleName = "标题 1" Or styleName = "标题 2" Or styleName = "标题 3" Or _
           styleName = "标题 4" Or styleName = "标题 5" Or styleName = "标题 6" Or _
           styleName = "标题 7" Or styleName = "标题 8" Or styleName = "标题 9" Then
            isSystemStyle = True
        End If

        ' 如果不是系统样式，则清除格式并改为正文
        If Not isSystemStyle Then
            para.Range.ClearFormatting
            para.Style = "正文"
            processedCount = processedCount + 1
        End If
    Next para

    Debug.Print "其他内容样式处理完成，共处理 " & processedCount & " 个段落"
End Sub

Function IsBuiltInStyle(currentStyle As String, targetStyle As String) As Boolean
    ' 判断是否为系统内置样式
    IsBuiltInStyle = (currentStyle = targetStyle)
End Function

Sub DeleteCustomStyles(doc As Document)
    ' 删除所有自定义样式
    Dim style As style
    Dim stylesToDelete As Collection
    Set stylesToDelete = New Collection

    ' 收集需要删除的自定义样式
    For Each style In doc.Styles
        If Not style.BuiltIn Then
            stylesToDelete.Add style
        End If
    Next style

    ' 删除自定义样式
    Dim i As Integer
    For i = 1 To stylesToDelete.Count
        On Error Resume Next
        stylesToDelete(i).Delete
        On Error GoTo 0
    Next i
End Sub

' 快速清理函数（简化版本）
Sub QuickStyleClean()
    ' 快速清理所有格式，全部改为正文
    Dim doc As Document
    Set doc = ActiveDocument

    Application.ScreenUpdating = False

    ' 选择全文
    doc.Range.Select

    ' 清除所有格式
    Selection.ClearFormatting

    ' 应用正文样式
    Selection.Style = "正文"

    ' 删除自定义样式
    Call DeleteCustomStyles(doc)

    Application.ScreenUpdating = True

    MsgBox "快速样式清理完成！", vbInformation
End Sub