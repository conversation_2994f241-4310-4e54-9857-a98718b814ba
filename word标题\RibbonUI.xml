<?xml version="1.0" encoding="UTF-8"?>
<customUI xmlns="http://schemas.microsoft.com/office/2009/07/customui" onLoad="RibbonOnLoad">
  <ribbon>
    <tabs>
      <tab id="TitleFormatTab" label="标题格式" insertAfterMso="TabHome">
        <group id="TitleGroup" label="标题设置">
          <button id="ApplyTitleFormat" 
                  label="应用标题格式" 
                  size="large"
                  imageMso="OutlineNumberingGallery"
                  onAction="ChangeParagraphStyle"
                  screentip="应用中文标题编号格式"
                  supertip="自动设置文档的多级标题编号格式：第一章、一、（一）、1、（1）、a."/>
          
          <button id="ResetFormat" 
                  label="重置格式" 
                  size="normal"
                  imageMso="ClearFormats"
                  onAction="ResetTitleFormat"
                  screentip="重置标题格式"
                  supertip="清除当前文档的标题编号格式"/>
          
          <button id="ShowHelp" 
                  label="使用说明" 
                  size="normal"
                  imageMso="Help"
                  onAction="ShowHelpDialog"
                  screentip="查看使用说明"
                  supertip="显示插件的详细使用说明"/>
        </group>
        
        <group id="QuickStyleGroup" label="快速样式">
          <button id="ApplyTitle1" 
                  label="标题1" 
                  size="normal"
                  imageMso="Heading1"
                  onAction="ApplyTitle1Style"
                  screentip="应用标题1样式"/>
          
          <button id="ApplyTitle2" 
                  label="标题2" 
                  size="normal"
                  imageMso="Heading2"
                  onAction="ApplyTitle2Style"
                  screentip="应用标题2样式"/>
          
          <button id="ApplyTitle3" 
                  label="标题3" 
                  size="normal"
                  imageMso="Heading3"
                  onAction="ApplyTitle3Style"
                  screentip="应用标题3样式"/>
          
          <button id="ApplyNormalText" 
                  label="正文" 
                  size="normal"
                  imageMso="NormalText"
                  onAction="ApplyNormalTextStyle"
                  screentip="应用正文样式"/>
        </group>
      </tab>
    </tabs>
  </ribbon>
</customUI>
