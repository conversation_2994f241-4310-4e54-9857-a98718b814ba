' ========================================
' Word标题格式插件 - 主模块
' 功能：自动设置中文多级标题编号格式
' 版本：1.0
' ========================================

Option Explicit

' 主要功能：应用标题格式（可直接调用的版本）
Sub 应用标题格式()
    ChangeParagraphStyle
End Sub

' 主要功能：应用标题格式
Sub ChangeParagraphStyle(Optional control As Object = Nothing)
    On Error GoTo ErrorHandler
    
    ' 检查是否有活动文档
    If ActiveDocument Is Nothing Then
        MsgBox "请先打开一个Word文档！", vbExclamation, "标题格式插件"
        Exit Sub
    End If
    
    ' 显示进度提示
    Application.ScreenUpdating = False
    Application.StatusBar = "正在应用标题格式..."
    
    ' 重置大纲编号库中的第5个编号模板，避免旧设置影响
    ListGalleries(wdOutlineNumberGallery).Reset (5)

    ' ========== 标题1 ==========
    With ListGalleries(wdOutlineNumberGallery).ListTemplates(5).ListLevels(1)
        .NumberFormat = "第%1章 "
        .NumberStyle = 37 ' 中文大写数字编号样式
        .NumberPosition = CentimetersToPoints(0)
        .Alignment = wdListLevelAlignCenter
        .TextPosition = CentimetersToPoints(0)
        .TabPosition = wdUndefined
        .ResetOnHigher = 0
        .StartAt = 1
        .LinkedStyle = "标题 1"
    End With
    With ActiveDocument.Styles("标题 1").Font
        .NameFarEast = "黑体"
        .NameAscii = "黑体"
        .NameOther = "黑体"
        .Name = "黑体"
        .Size = 15
        .Bold = True
        .Color = wdColorBlack
    End With
    With ActiveDocument.Styles("标题 1").ParagraphFormat
        .Alignment = wdAlignParagraphCenter
        .SpaceBefore = 24
        .SpaceAfter = 18
    End With

    ' ========== 标题2 ==========
    With ListGalleries(wdOutlineNumberGallery).ListTemplates(5).ListLevels(2)
        .NumberFormat = "%2、 "
        .NumberStyle = 37
        .NumberPosition = CentimetersToPoints(0)
        .Alignment = wdListLevelAlignLeft
        .TextPosition = CentimetersToPoints(0)
        .TabPosition = wdUndefined
        .ResetOnHigher = 1
        .StartAt = 1
        .LinkedStyle = "标题 2"
    End With
    With ActiveDocument.Styles("标题 2").Font
        .NameFarEast = "宋体"
        .NameAscii = "宋体"
        .NameOther = "宋体"
        .Name = "宋体"
        .Size = 14
        .Bold = True
        .Color = wdColorBlack
    End With
    With ActiveDocument.Styles("标题 2").ParagraphFormat
        .Alignment = wdAlignParagraphLeft
        .SpaceBefore = 12
        .SpaceAfter = 6
    End With

    ' ========== 标题3 ==========
    With ListGalleries(wdOutlineNumberGallery).ListTemplates(5).ListLevels(3)
        .NumberFormat = "（%3）"
        .NumberStyle = 37
        .NumberPosition = CentimetersToPoints(0.74)
        .Alignment = wdListLevelAlignLeft
        .TextPosition = CentimetersToPoints(0.74)
        .TabPosition = wdUndefined
        .ResetOnHigher = 2
        .StartAt = 1
        .LinkedStyle = "标题 3"
    End With
    With ActiveDocument.Styles("标题 3").Font
        .NameFarEast = "宋体"
        .NameAscii = "宋体"
        .NameOther = "宋体"
        .Name = "宋体"
        .Size = 12
        .Bold = True
        .Color = wdColorBlack
    End With
    With ActiveDocument.Styles("标题 3").ParagraphFormat
        .Alignment = wdAlignParagraphLeft
        .SpaceBefore = 6
        .SpaceAfter = 3
    End With

    ' ========== 标题4-6设置（简化版本） ==========
    ' 标题4
    With ListGalleries(wdOutlineNumberGallery).ListTemplates(5).ListLevels(4)
        .NumberFormat = "%4. "
        .NumberStyle = wdListNumberStyleArabic
        .NumberPosition = CentimetersToPoints(1.11)
        .Alignment = wdListLevelAlignLeft
        .TextPosition = CentimetersToPoints(1.75)
        .ResetOnHigher = 3
        .StartAt = 1
        .LinkedStyle = "标题 4"
    End With
    
    ' 标题5
    With ListGalleries(wdOutlineNumberGallery).ListTemplates(5).ListLevels(5)
        .NumberFormat = "(%5) "
        .NumberStyle = wdListNumberStyleArabic
        .NumberPosition = CentimetersToPoints(1)
        .Alignment = wdListLevelAlignLeft
        .TextPosition = CentimetersToPoints(0)
        .ResetOnHigher = 4
        .StartAt = 1
        .LinkedStyle = "标题 5"
    End With
    
    ' 标题6
    With ListGalleries(wdOutlineNumberGallery).ListTemplates(5).ListLevels(6)
        .NumberFormat = "%6. "
        .NumberStyle = wdListNumberStyleLowercaseLetter
        .NumberPosition = CentimetersToPoints(1.78)
        .Alignment = wdListLevelAlignLeft
        .TextPosition = CentimetersToPoints(2.44)
        .ResetOnHigher = 5
        .StartAt = 1
        .LinkedStyle = "标题 6"
    End With

    ' 设置标题4-6的字体格式
    Dim i As Integer
    For i = 4 To 6
        With ActiveDocument.Styles("标题 " & i).Font
            .NameFarEast = "宋体"
            .NameAscii = "宋体"
            .Name = "宋体"
            .Size = 12
            .Bold = IIf(i <= 4, True, False)
            .Color = wdColorBlack
        End With
        With ActiveDocument.Styles("标题 " & i).ParagraphFormat
            .Alignment = wdAlignParagraphLeft
            .SpaceBefore = 6
            .SpaceAfter = 6
        End With
    Next i

    ' 清除7-9级标题编号
    For i = 7 To 9
        With ListGalleries(wdOutlineNumberGallery).ListTemplates(5).ListLevels(i)
            .NumberFormat = ""
            .NumberStyle = wdListNumberStyleArabic
        End With
    Next i

    ' 应用模板
    ListGalleries(wdOutlineNumberGallery).ListTemplates(5).Name = ""
    Selection.Range.ListFormat.ApplyListTemplateWithLevel ListTemplate:= _
        ListGalleries(wdOutlineNumberGallery).ListTemplates(5), _
        ContinuePreviousList:=True, ApplyTo:=wdListApplyToWholeList, _
        DefaultListBehavior:=wdWord10ListBehavior

    ' ========== 正文样式设置 ==========
    With ActiveDocument.Styles("正文").Font
        .NameFarEast = "宋体"
        .NameAscii = "宋体"
        .Name = "宋体"
        .Size = 12
        .Bold = False
        .Color = wdColorBlack
    End With
    With ActiveDocument.Styles("正文").ParagraphFormat
        .Alignment = wdAlignParagraphLeft
        .SpaceBefore = 0
        .SpaceAfter = 6
        .LineSpacingRule = wdLineSpaceMultiple
        .LineSpacing = 1.2
        .FirstLineIndent = CentimetersToPoints(0.74)
        .LeftIndent = 0
        .RightIndent = 0
    End With

    ' 恢复屏幕更新
    Application.ScreenUpdating = True
    Application.StatusBar = False
    
    MsgBox "标题格式设置完成！" & vbCrLf & vbCrLf & _
           "编号格式：" & vbCrLf & _
           "标题1：第一章、第二章..." & vbCrLf & _
           "标题2：一、二、三..." & vbCrLf & _
           "标题3：（一）、（二）..." & vbCrLf & _
           "标题4：1.、2.、3..." & vbCrLf & _
           "标题5：(1)、(2)..." & vbCrLf & _
           "标题6：a.、b.、c...", vbInformation, "标题格式插件"
    
    Exit Sub
    
ErrorHandler:
    Application.ScreenUpdating = True
    Application.StatusBar = False
    MsgBox "设置标题格式时发生错误：" & vbCrLf & Err.Description, vbCritical, "错误"
End Sub

' 重置标题格式
Sub ResetTitleFormat(control As IRibbonControl)
    On Error GoTo ErrorHandler
    
    If MsgBox("确定要重置当前文档的标题格式吗？", vbYesNo + vbQuestion, "确认重置") = vbYes Then
        ' 重置大纲编号
        ListGalleries(wdOutlineNumberGallery).Reset (5)
        
        ' 清除选区的列表格式
        Selection.Range.ListFormat.RemoveNumbers
        
        MsgBox "标题格式已重置！", vbInformation, "标题格式插件"
    End If
    
    Exit Sub
    
ErrorHandler:
    MsgBox "重置格式时发生错误：" & vbCrLf & Err.Description, vbCritical, "错误"
End Sub

' 显示帮助对话框
Sub ShowHelpDialog(control As IRibbonControl)
    Dim helpText As String
    helpText = "标题格式插件使用说明：" & vbCrLf & vbCrLf & _
               "1. 点击【应用标题格式】按钮设置多级标题编号" & vbCrLf & _
               "2. 使用快速样式按钮快速应用标题样式" & vbCrLf & _
               "3. 点击【重置格式】清除标题编号" & vbCrLf & vbCrLf & _
               "编号格式说明：" & vbCrLf & _
               "• 标题1：第一章、第二章..." & vbCrLf & _
               "• 标题2：一、二、三..." & vbCrLf & _
               "• 标题3：（一）、（二）..." & vbCrLf & _
               "• 标题4：1.、2.、3..." & vbCrLf & _
               "• 标题5：(1)、(2)..." & vbCrLf & _
               "• 标题6：a.、b.、c..." & vbCrLf & vbCrLf & _
               "适用于：公文、学术论文、技术文档等"
    
    MsgBox helpText, vbInformation, "使用说明"
End Sub

' 快速应用标题样式
Sub ApplyTitle1Style(control As IRibbonControl)
    Selection.Style = "标题 1"
End Sub

Sub ApplyTitle2Style(control As IRibbonControl)
    Selection.Style = "标题 2"
End Sub

Sub ApplyTitle3Style(control As IRibbonControl)
    Selection.Style = "标题 3"
End Sub

Sub ApplyNormalTextStyle(control As IRibbonControl)
    Selection.Style = "正文"
End Sub
