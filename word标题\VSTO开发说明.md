# VSTO加载项开发说明

## 概述
使用Visual Studio Tools for Office (VSTO)可以创建更专业的Word加载项，具有更好的用户界面和部署能力。

## 开发环境要求
- Visual Studio 2019/2022
- .NET Framework 4.7.2+
- Office Developer Tools for Visual Studio
- Microsoft Office 2016+

## 项目创建步骤

### 1. 创建VSTO项目
```
文件 → 新建 → 项目
选择：Visual C# → Office/SharePoint → Office加载项
选择：Word VSTO加载项
项目名称：WordTitleFormatAddin
```

### 2. 主要文件结构
```
WordTitleFormatAddin/
├── ThisAddIn.cs              // 加载项主类
├── TitleFormatRibbon.cs      // 功能区界面
├── TitleFormatHelper.cs      // 标题格式处理类
├── Properties/
│   └── AssemblyInfo.cs       // 程序集信息
└── Resources/                // 资源文件
    └── icons/               // 图标文件
```

### 3. 核心代码示例

#### ThisAddIn.cs
```csharp
using System;
using Microsoft.Office.Tools;
using Word = Microsoft.Office.Interop.Word;

namespace WordTitleFormatAddin
{
    public partial class ThisAddIn
    {
        private void ThisAddIn_Startup(object sender, System.EventArgs e)
        {
            // 加载项启动时的初始化代码
        }

        private void ThisAddIn_Shutdown(object sender, System.EventArgs e)
        {
            // 加载项关闭时的清理代码
        }

        #region VSTO 生成的代码
        private void InternalStartup()
        {
            this.Startup += new System.EventHandler(ThisAddIn_Startup);
            this.Shutdown += new System.EventHandler(ThisAddIn_Shutdown);
        }
        #endregion
    }
}
```

#### TitleFormatHelper.cs
```csharp
using System;
using Word = Microsoft.Office.Interop.Word;

namespace WordTitleFormatAddin
{
    public static class TitleFormatHelper
    {
        public static void ApplyChineseTitleFormat()
        {
            try
            {
                Word.Application app = Globals.ThisAddIn.Application;
                Word.Document doc = app.ActiveDocument;
                
                if (doc == null)
                {
                    System.Windows.Forms.MessageBox.Show("请先打开一个Word文档！", 
                        "标题格式插件", 
                        System.Windows.Forms.MessageBoxButtons.OK, 
                        System.Windows.Forms.MessageBoxIcon.Warning);
                    return;
                }

                // 重置大纲编号模板
                app.ListGalleries[Word.WdListGalleryType.wdOutlineNumberGallery].Reset(5);

                // 设置标题1格式
                SetTitleLevel1(app);
                SetTitleLevel2(app);
                SetTitleLevel3(app);
                // ... 其他级别

                // 应用模板
                ApplyListTemplate(app);
                
                // 设置正文样式
                SetNormalStyle(doc);

                System.Windows.Forms.MessageBox.Show("标题格式设置完成！", 
                    "标题格式插件", 
                    System.Windows.Forms.MessageBoxButtons.OK, 
                    System.Windows.Forms.MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                System.Windows.Forms.MessageBox.Show($"设置标题格式时发生错误：{ex.Message}", 
                    "错误", 
                    System.Windows.Forms.MessageBoxButtons.OK, 
                    System.Windows.Forms.MessageBoxIcon.Error);
            }
        }

        private static void SetTitleLevel1(Word.Application app)
        {
            var level1 = app.ListGalleries[Word.WdListGalleryType.wdOutlineNumberGallery]
                .ListTemplates[5].ListLevels[1];
            
            level1.NumberFormat = "第%1章 ";
            level1.NumberStyle = Word.WdListNumberStyle.wdListNumberStyleSimpChinNum2;
            level1.NumberPosition = app.CentimetersToPoints(0);
            level1.Alignment = Word.WdListLevelAlignment.wdListLevelAlignCenter;
            level1.TextPosition = app.CentimetersToPoints(0);
            level1.ResetOnHigher = 0;
            level1.StartAt = 1;
            level1.LinkedStyle = "标题 1";

            // 设置标题1字体样式
            var style1 = app.ActiveDocument.Styles["标题 1"];
            style1.Font.NameFarEast = "黑体";
            style1.Font.NameAscii = "黑体";
            style1.Font.Size = 15;
            style1.Font.Bold = -1; // True
            style1.Font.Color = Word.WdColor.wdColorBlack;
            
            style1.ParagraphFormat.Alignment = Word.WdParagraphAlignment.wdAlignParagraphCenter;
            style1.ParagraphFormat.SpaceBefore = 24;
            style1.ParagraphFormat.SpaceAfter = 18;
        }

        private static void SetTitleLevel2(Word.Application app)
        {
            var level2 = app.ListGalleries[Word.WdListGalleryType.wdOutlineNumberGallery]
                .ListTemplates[5].ListLevels[2];
            
            level2.NumberFormat = "%2、 ";
            level2.NumberStyle = Word.WdListNumberStyle.wdListNumberStyleSimpChinNum2;
            level2.NumberPosition = app.CentimetersToPoints(0);
            level2.Alignment = Word.WdListLevelAlignment.wdListLevelAlignLeft;
            level2.TextPosition = app.CentimetersToPoints(0);
            level2.ResetOnHigher = 1;
            level2.StartAt = 1;
            level2.LinkedStyle = "标题 2";

            // 设置标题2字体样式
            var style2 = app.ActiveDocument.Styles["标题 2"];
            style2.Font.NameFarEast = "宋体";
            style2.Font.NameAscii = "宋体";
            style2.Font.Size = 14;
            style2.Font.Bold = -1;
            style2.Font.Color = Word.WdColor.wdColorBlack;
            
            style2.ParagraphFormat.Alignment = Word.WdParagraphAlignment.wdAlignParagraphLeft;
            style2.ParagraphFormat.SpaceBefore = 12;
            style2.ParagraphFormat.SpaceAfter = 6;
        }

        private static void SetTitleLevel3(Word.Application app)
        {
            var level3 = app.ListGalleries[Word.WdListGalleryType.wdOutlineNumberGallery]
                .ListTemplates[5].ListLevels[3];
            
            level3.NumberFormat = "（%3）";
            level3.NumberStyle = Word.WdListNumberStyle.wdListNumberStyleSimpChinNum2;
            level3.NumberPosition = app.CentimetersToPoints(0.74f);
            level3.Alignment = Word.WdListLevelAlignment.wdListLevelAlignLeft;
            level3.TextPosition = app.CentimetersToPoints(0.74f);
            level3.ResetOnHigher = 2;
            level3.StartAt = 1;
            level3.LinkedStyle = "标题 3";

            // 设置标题3字体样式
            var style3 = app.ActiveDocument.Styles["标题 3"];
            style3.Font.NameFarEast = "宋体";
            style3.Font.NameAscii = "宋体";
            style3.Font.Size = 12;
            style3.Font.Bold = -1;
            style3.Font.Color = Word.WdColor.wdColorBlack;
            
            style3.ParagraphFormat.Alignment = Word.WdParagraphAlignment.wdAlignParagraphLeft;
            style3.ParagraphFormat.SpaceBefore = 6;
            style3.ParagraphFormat.SpaceAfter = 3;
        }

        private static void ApplyListTemplate(Word.Application app)
        {
            var template = app.ListGalleries[Word.WdListGalleryType.wdOutlineNumberGallery].ListTemplates[5];
            template.Name = "";
            
            app.Selection.Range.ListFormat.ApplyListTemplateWithLevel(
                ListTemplate: template,
                ContinuePreviousList: true,
                ApplyTo: Word.WdListApplyTo.wdListApplyToWholeList,
                DefaultListBehavior: Word.WdDefaultListBehavior.wdWord10ListBehavior);
        }

        private static void SetNormalStyle(Word.Document doc)
        {
            var normalStyle = doc.Styles["正文"];
            normalStyle.Font.NameFarEast = "宋体";
            normalStyle.Font.NameAscii = "宋体";
            normalStyle.Font.Size = 12;
            normalStyle.Font.Bold = 0; // False
            normalStyle.Font.Color = Word.WdColor.wdColorBlack;
            
            normalStyle.ParagraphFormat.Alignment = Word.WdParagraphAlignment.wdAlignParagraphLeft;
            normalStyle.ParagraphFormat.SpaceBefore = 0;
            normalStyle.ParagraphFormat.SpaceAfter = 6;
            normalStyle.ParagraphFormat.LineSpacingRule = Word.WdLineSpacing.wdLineSpaceMultiple;
            normalStyle.ParagraphFormat.LineSpacing = 1.2f;
            normalStyle.ParagraphFormat.FirstLineIndent = doc.Application.CentimetersToPoints(0.74f);
        }
    }
}
```

## 部署和分发

### 1. ClickOnce部署
- 项目属性 → 发布
- 设置发布位置和安装URL
- 配置先决条件（.NET Framework、Office Runtime）
- 生成安装程序

### 2. Windows Installer部署
- 使用Visual Studio Installer Projects
- 创建Setup项目
- 添加项目输出和依赖项
- 配置注册表项和文件关联

### 3. 企业部署
- 使用组策略部署
- SCCM部署
- 网络共享部署

## 优势对比

### VSTO vs VBA
| 特性 | VSTO | VBA |
|------|------|-----|
| 开发语言 | C#/VB.NET | VBA |
| 调试能力 | 强 | 一般 |
| 部署方式 | 多种 | 手动 |
| 安全性 | 高 | 一般 |
| 维护性 | 好 | 一般 |
| 学习成本 | 高 | 低 |

## 建议
- **简单需求**：使用VBA方案，快速简单
- **企业应用**：使用VSTO方案，更专业可靠
- **个人使用**：VBA加载项即可满足需求
- **团队协作**：VSTO便于版本控制和团队开发
